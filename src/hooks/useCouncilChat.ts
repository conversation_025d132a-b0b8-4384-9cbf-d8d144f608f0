import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useAppStore } from '../stores/useAppStore';
import MessageService from '../services/centrifugo/MessageService';
import { chatService } from '../services/api/chatService';
import { CentrifugoMessage } from '../types';
import type { CouncilMessage, UseCouncilChatReturn } from '../types';
import config from '../config';

export const useCouncilChat = (): UseCouncilChatReturn => {
  const [messages, setMessages] = useState<CouncilMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentChannel, setCurrentChannel] = useState<string>();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  const store = useAppStore();
  const messageService = MessageService.getInstance();

  // Constants from config
  const HARDCODED_USER_ID = config.defaults.userId;
  const HARDCODED_CHARACTER_ID = config.defaults.characterId;
  const HARDCODED_OBJECTIVE_ID = config.defaults.objectiveId;

  // Handle incoming messages from Centrifugo
  const handleCentrifugoMessage = useCallback(
    async (centrifugoMessage: CentrifugoMessage, channel: string) => {
      console.log(
        'Council chat received message:',
        centrifugoMessage,
        'on channel:',
        channel,
      );

      if (centrifugoMessage.data) {
        const newMessage: CouncilMessage = {
          id: `received-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`,
          text:
            centrifugoMessage.data.text ||
            centrifugoMessage.data.message?.text ||
            JSON.stringify(centrifugoMessage.data),
          user:
            centrifugoMessage.data.user ||
            centrifugoMessage.data.message?.chatRole ||
            'Assistant',
          timestamp: centrifugoMessage.data.timestamp || Date.now(),
          type: 'received',
          channel: channel,
          characterId: centrifugoMessage.data.characterId,
          objectiveId: centrifugoMessage.data.objectiveId,
        };

        setMessages(prev => {
          // Avoid duplicate messages
          const isDuplicate = prev.some(
            msg =>
              msg.text === newMessage.text &&
              msg.user === newMessage.user &&
              Math.abs(msg.timestamp - newMessage.timestamp) < 1000,
          );

          if (isDuplicate) {
            return prev;
          }

          return [...prev, newMessage];
        });
      }
    },
    [],
  );

  // Initialize connection and load history
  useEffect(() => {
    const initializeChat = async () => {
      try {
        // Initialize message service if not already done
        if (!messageService.isConnected()) {
          await messageService.initialize(HARDCODED_USER_ID);
        }

        // Subscribe to council channel
        const success = await messageService.subscribeToCouncilChannel(
          HARDCODED_USER_ID,
          HARDCODED_CHARACTER_ID,
        );

        if (success) {
          const channel = messageService.generateCouncilChannelId(
            HARDCODED_USER_ID,
            HARDCODED_CHARACTER_ID,
          );
          setCurrentChannel(channel);

          // Add message handler
          messageService.addCouncilMessageHandler(handleCentrifugoMessage);

          // Load chat history
          setIsLoading(true);
          try {
            const result = await chatService.getHistory(
              HARDCODED_USER_ID,
              HARDCODED_CHARACTER_ID,
            );

            if (result.success && result.messages) {
              const historyMessages: CouncilMessage[] = result.messages.map(
                (msg: any) => ({
                  ...msg,
                  channel: channel,
                }),
              );

              setMessages(historyMessages);
            }
          } catch (error) {
            console.error('Error loading chat history:', error);
          } finally {
            setIsLoading(false);
          }
        }

        console.log('Council chat initialized');
      } catch (error) {
        console.error('Failed to initialize council chat:', error);
      }
    };

    initializeChat();

    return () => {
      // Cleanup on unmount
      messageService.removeCouncilMessageHandler(handleCentrifugoMessage);
      messageService.unsubscribeFromCouncilChannel(
        HARDCODED_USER_ID,
        HARDCODED_CHARACTER_ID,
      );
    };
  }, [
    handleCentrifugoMessage,
    messageService,
    HARDCODED_USER_ID,
    HARDCODED_CHARACTER_ID,
    HARDCODED_OBJECTIVE_ID,
  ]);

  // Monitor connection status changes
  useEffect(() => {
    const updateConnectionStatus = () => {
      const connected = messageService.isConnected();
      const status = messageService.getConnectionStatus();

      console.log('🔍 Hook connection status update:', {
        connected,
        status,
        currentConnected: isConnected,
        currentStatus: connectionStatus,
      });

      setIsConnected(connected);
      setConnectionStatus(status);
    };

    // Update immediately
    updateConnectionStatus();

    // Set up interval to check status periodically
    const statusInterval = setInterval(updateConnectionStatus, 1000);

    return () => {
      clearInterval(statusInterval);
    };
  }, [messageService, isConnected, connectionStatus]);

  const sendMessage = useCallback(
    async (
      text: string,
      characterId: string = HARDCODED_CHARACTER_ID,
      objectiveId: string = HARDCODED_OBJECTIVE_ID,
    ): Promise<boolean> => {
      if (!text.trim()) {
        return false;
      }

      if (!messageService.isConnected()) {
        Alert.alert('Error', 'Not connected to chat service');
        return false;
      }

      setIsLoading(true);

      try {
        const user = store.auth.user || store.user.profile;
        const userName = user?.displayName || user?.alias || 'You';

        // Add message to local state immediately (optimistic update)
        const sentMessage: CouncilMessage = {
          id: `sent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          text: text.trim(),
          user: userName,
          timestamp: Date.now(),
          type: 'sent',
          channel: currentChannel,
          characterId,
          objectiveId,
        };

        setMessages(prev => [...prev, sentMessage]);

        // Send message via API
        const result = await chatService.sendMessage(
          text.trim(),
          HARDCODED_USER_ID,
          characterId,
          objectiveId,
        );

        if (!result.success) {
          // Remove the optimistically added message on error
          setMessages(prev => prev.filter(msg => msg.id !== sentMessage.id));
          throw new Error(result.error || 'Failed to send message');
        }

        console.log(`Message sent successfully to character ${characterId}`);
        return true;
      } catch (error) {
        console.error('Error sending message:', error);
        Alert.alert('Error', 'Failed to send message. Please try again.');
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [
      messageService,
      store.auth.user,
      store.user.profile,
      currentChannel,
      HARDCODED_USER_ID,
      HARDCODED_CHARACTER_ID,
      HARDCODED_OBJECTIVE_ID,
    ],
  );

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const testConnection = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    try {
      const isConnected = messageService.isConnected();

      if (isConnected) {
        // Add a test message to local state
        const testMessage: CouncilMessage = {
          id: `test-${Date.now()}`,
          text: 'Connection test successful!',
          user: 'System',
          timestamp: Date.now(),
          type: 'received',
          channel: 'system',
        };
        setMessages(prev => [...prev, testMessage]);
      }

      return isConnected;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [messageService]);

  const subscribeToCharacter = useCallback(
    async (characterId: string): Promise<boolean> => {
      try {
        // Unsubscribe from current character if any
        if (currentChannel) {
          messageService.removeCouncilMessageHandler(handleCentrifugoMessage);
          messageService.unsubscribeFromCouncilChannel(
            HARDCODED_USER_ID,
            HARDCODED_CHARACTER_ID,
          );
        }

        // Subscribe to new character
        const success = await messageService.subscribeToCouncilChannel(
          HARDCODED_USER_ID,
          characterId,
        );

        if (success) {
          const channel = messageService.generateCouncilChannelId(
            HARDCODED_USER_ID,
            characterId,
          );
          setCurrentChannel(channel);
          messageService.addCouncilMessageHandler(handleCentrifugoMessage);

          // Load history for new character
          setIsLoading(true);
          try {
            const historyResult = await chatService.getHistory(
              HARDCODED_USER_ID,
              characterId,
            );

            if (historyResult.success && historyResult.messages) {
              const historyMessages: CouncilMessage[] =
                historyResult.messages.map((msg: any) => ({
                  ...msg,
                  channel: channel,
                }));

              setMessages(historyMessages);
            }
          } catch (error) {
            console.error('Error loading character history:', error);
          } finally {
            setIsLoading(false);
          }
        }

        return success;
      } catch (error) {
        console.error(
          `Failed to subscribe to character ${characterId}:`,
          error,
        );
        return false;
      }
    },
    [
      currentChannel,
      handleCentrifugoMessage,
      messageService,
      HARDCODED_USER_ID,
      HARDCODED_CHARACTER_ID,
    ],
  );

  const unsubscribeFromCharacter = useCallback(
    (characterId: string): void => {
      messageService.removeCouncilMessageHandler(handleCentrifugoMessage);
      messageService.unsubscribeFromCouncilChannel(
        HARDCODED_USER_ID,
        characterId,
      );
      setCurrentChannel(undefined);
      console.log(`Unsubscribed from character ${characterId}`);
    },
    [handleCentrifugoMessage, messageService, HARDCODED_USER_ID],
  );

  return {
    messages,
    isLoading,
    isConnected,
    connectionStatus,
    currentChannel,
    sendMessage,
    clearMessages,
    testConnection,
    subscribeToCharacter,
    unsubscribeFromCharacter,
  };
};

export default useCouncilChat;
