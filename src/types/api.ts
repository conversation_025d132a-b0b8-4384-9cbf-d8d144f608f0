// API Types
export interface ApiResponse<T = any> {
  success: boolean;
  requestId: string;
  message?: string;
  data?: T;
}

export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: string;
}

// Error Types
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUBSCRIPTION_REQUIRED = 'SUBSCRIPTION_REQUIRED',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
}

export class APIError extends Error {
  type: ErrorType;
  code: string;
  details?: any;
  timestamp: Date;

  constructor(
    type: ErrorType,
    message: string,
    code: string,
    details?: any,
    timestamp?: Date,
  ) {
    super(message);
    this.name = 'APIError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = timestamp || new Date();
  }
}
