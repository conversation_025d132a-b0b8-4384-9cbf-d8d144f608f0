export interface CouncilCharacter {
  id: string;
  name: string;
  description?: string;
}

export interface CouncilObjective {
  id: string;
  name: string;
  description?: string;
}

export interface CouncilMessage {
  id: string;
  text: string;
  user: string;
  timestamp: number;
  type: 'sent' | 'received';
  channel?: string;
  characterId?: string;
  objectiveId?: string;
}

export interface CouncilChatState {
  messages: CouncilMessage[];
  isLoading: boolean;
  isConnected: boolean;
  connectionStatus: string;
  currentChannel?: string;
}

export interface CouncilChatActions {
  sendMessage: (
    text: string,
    characterId: string,
    objectiveId: string
  ) => Promise<boolean>;
  clearMessages: () => void;
  testConnection: () => Promise<boolean>;
  subscribeToCharacter: (characterId: string) => Promise<boolean>;
  unsubscribeFromCharacter: (characterId: string) => void;
}

export type UseCouncilChatReturn = CouncilChatState & CouncilChatActions;
