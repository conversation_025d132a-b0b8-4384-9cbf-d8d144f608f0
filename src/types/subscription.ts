// Subscription Types
export interface Subscription {
  id: string;
  userId: string;
  plan: 'FREE' | 'PREMIUM';
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED';
  startDate: Date;
  endDate?: Date;
  paymentMethod: string;
  nextBillingDate?: Date;
  features: SubscriptionFeatures;
}

export interface SubscriptionFeatures {
  maxActiveObjectives: number;
  goalModificationsPerMonth: number;
  questModificationsPerMonth: number;
  dailyMessageLimit: number;
  calendarIntegrations: string[];
  advancedAI: boolean;
  themes: string[];
}
