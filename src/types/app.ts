//TODO: Deeplinks generation
//TODO: React native keychain

import { User, UserState } from './user';
import { ObjectivesState, QuestsState } from './objective';
import { ChatState } from './chat';

export interface PendingRequest {
  requestId: string;
  type: string;
  timestamp: number;
  timeout: NodeJS.Timeout;
  resolve: (data: any) => void;
  reject: (error: Error) => void;
}

// State Types
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface UIState {
  theme: AppTheme;
  activeTab: 'objectives' | 'arena' | 'council' | 'profile' | 'notifications';
  modals: {
    profile: boolean;
    gameMaster: boolean;
    subscription: boolean;
  };
  toasts: Toast[];
}

export interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration: number;
}

export interface RealtimeState {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  pendingRequests: Map<string, PendingRequest>;
  lastHeartbeat: number;
  reconnectAttempts: number;
}

export interface NavigationState {
  currentRoute: string;
  previousRoutes: string[];
}

export interface AppState {
  auth: AuthState;
  user: UserState;
  objectives: ObjectivesState;
  quests: QuestsState;
  chat: ChatState;
  ui: UIState;
  navigation: NavigationState;
  realtime: RealtimeState;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Onboarding: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
};

export type MainTabParamList = {
  Objectives: undefined;
  Arena: undefined;
  Council: undefined;
  Profile: undefined;
};

export type ObjectivesStackParamList = {
  ObjectivesList: undefined;
  ObjectiveJourney: { objectiveId: string };
  CreateObjective: undefined;
  CalendarView: undefined;
};

// Utility Types

export interface PendingAction {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

export interface ErrorContext {
  operation: string;
  timestamp: number;
  userId?: string;
  requestId?: string;
}

// Component Props Types

export interface HeaderProps {
  title: string;
  subtitle?: string;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
}

//TODO: Shopify restyle
export interface AppTheme {
  name: string;
  journeyTheme: JourneyTheme;
}

export interface JourneyTheme {
  name: string;
  // Global Colors
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    error: string;
    success: string;
    warning: string;
    text: string;
    textSecondary: string;
    border: string;
    disabled: string;
  };

  // Typography
  typography: {
    fontFamily: string;
    fontFamilyBold?: string;
    fontSizes: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
    };
    lineHeights: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
    };
  };

  // Buttons
  buttons: {
    default: {
      backgroundColor: string;
      textColor: string;
      borderRadius: number;
      paddingVertical: number;
      paddingHorizontal: number;
      elevation?: number; // shadow depth
    };
    primary: {
      backgroundColor: string;
      textColor: string;
    };
    secondary: {
      backgroundColor: string;
      textColor: string;
    };
    disabled: {
      backgroundColor: string;
      textColor: string;
    };
  };

  // Inputs
  inputs: {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    placeholderColor: string;
    focusBorderColor: string;
    errorBorderColor: string;
    borderRadius: number;
    padding: number;
  };

  // Cards
  cards: {
    backgroundColor: string;
    borderRadius: number;
    shadowColor: string;
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
    padding: number;
  };

  // Animations
  animations: {
    duration: {
      fast: number;
      normal: number;
      slow: number;
    };
    easing: {
      linear: (value: number) => number;
      easeIn: (value: number) => number;
      easeOut: (value: number) => number;
      easeInOut: (value: number) => number;
    };
    transitions: {
      fadeIn: number;
      fadeOut: number;
      slideIn: number;
      slideOut: number;
    };
  };

  // Iconography
  icons: {
    size: {
      sm: number;
      md: number;
      lg: number;
    };
    color: string;
  };
  activeTab: 'objectives' | 'arena' | 'council' | 'profile' | 'notifications';
  modals: {
    gameMaster: boolean;
    subscription: boolean;
  };
  toasts: Toast[];
}
