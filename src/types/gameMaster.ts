// Game Master Types
export interface GameMaster {
  id: string;
  name: string;
  description: string;
  avatarUrl: string;
  tone: 'FRIENDLY' | 'MOTIVATIONAL' | 'STRICT' | 'CASUAL';
  theme: string;
  specialties: string[];
  isActive: boolean;
  personality: {
    supportiveLevel: number; // 1-10
    strictnessLevel: number; // 1-10
    humorLevel: number; // 1-10
  };
}

export interface GameMasterCardProps {
  gameMaster: GameMaster;
  isSelected: boolean;
  onSelect: (characterId: string) => void;
}
