import { PreviewCard } from './chat';

// Objective Types
export interface ObjectiveJourney {
  id: string;
  userId: string;
  title: string;
  summary: string;
  startDate: Date;
  endDate: Date;
  period: string; // number + unit to be discussed
  status: 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  totalXp: number;
  currentXp: number;
  journeyDifficulty: 'EASY' | 'MEDIUM' | 'HARD';
  characterId: string;
  levels: Level[];
  options: Map<string, string>;
  quests: Quest[];
  totalQuests: number;
  journeyCategories: string[];
  questCountByDifficulty: Map<string, number>;
  createdAt: Date;
  journeySummary: string;
}

export interface Level {
  id: string;
  levelNumber: number;
  title: string;
  description: string;
  xpRequired: number;
  completionPeriod: number;
  isCompleted: boolean;
  completedAt?: Date;
}

// Quest Types
export interface Quest {
  id: string;
  objectiveId: string;
  userId: string;
  title: string;
  description: string;
  hint: string;
  type: 'NORMAL' | 'SIDE';
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  xp: number;
  calendarTitle: string;
  calendarDescription: string;
  scheduledDate: Date;
  scheduledTime: string; // HH:MM format
  isAllDay: Date;
  timezone: string;
  recurrence: number;
  remindOffsets: number[];
  tags: string[];
  status: 'PENDING' | 'COMPLETED' | 'SKIPPED';
  completedAt?: Date;
  category: string;
  estimatedDuration: number; // in minutes
  createdAt: Date;
}

export interface ObjectivesState {
  objectives: ObjectiveJourney[];
  currentObjective: ObjectiveJourney | null;
  loading: boolean;
  filters: ObjectiveFilters;
}

export interface ObjectiveFilters {
  status: 'all' | 'active' | 'completed' | 'paused';
  dateRange: 'all' | 'week' | 'month';
}

export interface QuestsState {
  todayQuests: Quest[];
  allQuests: Quest[];
  loading: boolean;
  completingQuests: Set<string>;
}

export interface QuestCardProps {
  quest: Quest;
  onComplete: (questId: string) => void;
  isCompleting: boolean;
}

export interface ObjectiveCardProps {
  objective: ObjectiveJourney;
  onPress: (objectiveId: string) => void;
}

export interface PreviewCardProps {
  preview: PreviewCard;
  onAccept: (previewId: string) => void;
  onReject: (previewId: string) => void;
}

// Form Types
export interface CreateObjectiveRequest {
  title: string;
  summary: string;
  startDate: Date;
  period: string;
  journeyDifficulty: 'EASY' | 'MEDIUM' | 'HARD';
  options: Map<string, string>;
  journeyCategories: string[];
}

export interface UpdateObjectiveRequest {
  title?: string;
  summary?: string;
  startDate?: Date;
  endDate?: Date;
  period?: string;
  status?: 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  journeyDifficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  options?: Map<string, string>;
  journeyCategories?: string[];
}

export interface CreateQuestRequest {
  objectiveId: string;
  title: string;
  description: string;
  hint?: string;
  type: 'NORMAL' | 'SIDE';
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  xp: number;
  scheduledDate: Date;
  scheduledTime: string;
  category: string;
  estimatedDuration: number;
  tags?: string[];
}

export interface UpdateQuestRequest {
  title?: string;
  description?: string;
  hint?: string;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  xp?: number;
  scheduledDate?: Date;
  scheduledTime?: string;
  category?: string;
  estimatedDuration?: number;
  tags?: string[];
}
