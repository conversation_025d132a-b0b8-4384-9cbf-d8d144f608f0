// Core User Types
export interface User {
  id: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  profilePictureUrl: string;
  alias: string;
  timezone: string;
  membership: 'FREE' | 'PREMIUM';
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
  isOnboarded: boolean;
}

export interface UserPreferences {
  selectedGameMaster: string;
  language: string;
}

export interface UserState {
  profile: User | null;
  stats: UserStats;
  loading: boolean;
}

export interface UserStats {
  totalXP: number;
  completedQuests: number;
  activeObjectives: number;
  streak: number;
  level: number;
}

export interface UpdateUserPreferencesRequest {
  selectedTheme?: string;
  selectedGameMaster?: string;
}
