import { Objective<PERSON><PERSON><PERSON>, <PERSON> } from './objective';

// Chat Types
export interface ChatMessage {
  id: string;
  timestamp: Date;
  messageType:
    | 'SIMPLE'
    | 'THINKING'
    | 'NEW_GOAL_PLAN_PREVIEW'
    | 'MEDIA'
    | 'QUEST_MODIFICATION'
    | 'NEW_DYNAMIC_QUEST'
    | 'MODIFIED_GOAL_PLAN_PREVIEW';
  message:
    | SimpleMessage
    | ThinkingMessage
    | NewGoalPlanPreviewMessage
    | MediaMessage
    | QuestModificationMessage
    | NewDynamicQuestMessage
    | ModifiedGoalPlanPreviewMessage;
}

export interface BaseMessage {
  text: string;
  language: string;
  chatRole: 'USER' | 'ASSISTANT';
}

export interface SimpleMessage extends BaseMessage {
  // Only contains base message fields
}

export interface ThinkingMessage extends BaseMessage {
  // Only contains base message fields
}

export interface MediaMessage extends BaseMessage {
  attachment: MediaAttachment;
}

export interface NewGoalPlanPreviewMessage extends BaseMessage {
  objectiveJourney: ObjectiveJourney;
}

export interface QuestModificationMessage extends BaseMessage {
  quest: Quest;
  changes: QuestChange[];
  reason: string;
}

export interface NewDynamicQuestMessage extends BaseMessage {
  quest: Quest;
  reason: string;
}

export interface ModifiedGoalPlanPreviewMessage extends BaseMessage {
  objectiveJourney: ObjectiveJourney;
  changes: ObjectiveChange[];
  reason: string;
}

// Supporting Types for Messages
export interface MediaAttachment {
  url: string;
  fileName: string;
  mimeType: string;
  mediaType: 'IMAGE' | 'PDF' | 'VIDEO' | 'AUDIO' | 'DOCUMENT';
  sizeBytes: number;
  width?: number | null;
  height?: number | null;
  durationSec?: number | null;
  thumbnailUrl?: string | null;
}

export interface QuestChange {
  name: string;
  changeType: 'ADDED' | 'MODIFIED' | 'REMOVED';
  previous: string;
  current: string;
}

export interface ObjectiveChange {
  name: string;
  changeType: 'ADDED' | 'MODIFIED' | 'REMOVED';
  previous: string;
  current: string;
}

export interface PreviewCard {
  id: string;
  type: 'NEW_GOAL' | 'MODIFIED_GOAL' | 'QUEST_MODIFICATION' | 'DYNAMIC_QUEST';
  data: any; // Polymorphic based on type
  isAccepted?: boolean;
}

// Real-time Types
export interface CentrifugoMessage<T = any> {
  type: MessageType;
  requestId?: string;
  data: T;
  timestamp: number;
  version?: string;
}

export enum MessageType {
  // Data updates
  OBJECTIVES_UPDATED = 'objectives.updated',
  QUESTS_UPDATED = 'quests.updated',
  QUEST_COMPLETED = 'quest.completed',
  LEVEL_UP = 'level.up',
  XP_GAINED = 'xp.gained',

  // Chat messages
  CHAT_MESSAGE = 'chat.message',
  CHAT_PREVIEW_CARD = 'chat.preview_card',

  // Notifications
  NOTIFICATION_RECEIVED = 'notification.received',

  // Error handling
  ERROR_OCCURRED = 'error.occurred',
  RATE_LIMITED = 'rate.limited',
}

export interface ChatState {
  messages: ChatMessage[];
  loading: boolean;
  typing: boolean;
  pendingPreviews: PreviewCard[];
}

export type MessageHandler = (
  message: CentrifugoMessage,
  channel: string,
) => Promise<void>;

export interface ChannelStructure {
  userChat: `user:${string}:chat`;
  notifications: `user:${string}:notifications`;
}
