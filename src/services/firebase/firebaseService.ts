import { Platform } from 'react-native';
import { firebase } from '@react-native-firebase/app';
import '@react-native-firebase/remote-config';
import '@react-native-firebase/analytics';
import '@react-native-firebase/auth';

/**
 * Firebase Configuration Service
 * Handles Firebase initialization and configuration
 */
export class FirebaseConfigService {
  private static instance: FirebaseConfigService;
  private initialized = false;

  private constructor() {}

  static getInstance(): FirebaseConfigService {
    if (!FirebaseConfigService.instance) {
      FirebaseConfigService.instance = new FirebaseConfigService();
    }
    return FirebaseConfigService.instance;
  }

  /**
   * Initialize Firebase with platform-specific configuration
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('Firebase already initialized');
      return;
    }

    try {
      // Check if Firebase is already initialized
      if (firebase.apps.length === 0) {
        console.log('Initializing Firebase...');
        
        // Firebase will automatically use the configuration files:
        // - iOS: GoogleService-Info.plist
        // - Android: google-services.json
        
        // The configuration is automatically loaded by the native Firebase SDK
        // No manual configuration needed when using the config files
        
        console.log('Firebase initialized successfully');
      } else {
        console.log('Firebase already has apps initialized');
      }

      // Verify Firebase services are available
      await this.verifyServices();
      
      this.initialized = true;
      console.log('Firebase configuration completed');
      
    } catch (error) {
      console.error('Failed to initialize Firebase:', error);
      throw error;
    }
  }

  /**
   * Verify that Firebase services are properly configured
   */
  private async verifyServices(): Promise<void> {
    try {
      // Test Remote Config
      const remoteConfig = firebase.remoteConfig();
      console.log('Remote Config available:', !!remoteConfig);

      // Test Analytics
      const analytics = firebase.analytics();
      console.log('Analytics available:', !!analytics);

      // Test Firebase Auth
      const auth = firebase.auth();
      console.log('Auth available:', !!auth);

      // Log the current Firebase app configuration (without sensitive data)
      const app = firebase.app();
      console.log('Firebase app initialized:', {
        name: app.name,
        platform: Platform.OS,
        options: {
          projectId: app.options.projectId,
          appId: app.options.appId,
          // Don't log sensitive keys
        }
      });

    } catch (error) {
      console.error('Firebase services verification failed:', error);
      throw error;
    }
  }

  /**
   * Get Firebase app instance
   */
  getApp() {
    if (!this.initialized) {
      throw new Error('Firebase not initialized. Call initialize() first.');
    }
    return firebase.app();
  }

  /**
   * Check if Firebase is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Enable or disable Firebase Analytics data collection
   */
  async setAnalyticsEnabled(enabled: boolean): Promise<void> {
    try {
      await firebase.analytics().setAnalyticsCollectionEnabled(enabled);
      console.log(`Firebase Analytics data collection ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Failed to set Analytics collection enabled:', error);
    }
  }

  /**
   * Set user properties for Analytics
   */
  async setUserProperties(properties: Record<string, string>): Promise<void> {
    try {
      const analytics = firebase.analytics();
      for (const [key, value] of Object.entries(properties)) {
        await analytics.setUserProperty(key, value);
      }
      console.log('User properties set:', Object.keys(properties));
    } catch (error) {
      console.error('Failed to set user properties:', error);
    }
  }
}

// Export singleton instance
export const firebaseConfigService = FirebaseConfigService.getInstance();

// Export default for easy importing
export default firebaseConfigService;
