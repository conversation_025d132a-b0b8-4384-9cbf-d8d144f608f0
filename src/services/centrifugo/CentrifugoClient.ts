import config from '../../config/appConfig.prod';
import {
  CentrifugoMessage,
  MessageType,
  MessageHandler,
  ErrorContext,
} from '../../types';
import { Centrifuge } from 'centrifuge';

// Types for Centrifuge client
interface CentrifugeClient {
  connect(): void;
  disconnect(): void;
  newSubscription(channel: string): Subscription;
  on(event: string, handler: Function): void;
  removeAllListeners(): void;
}

interface Subscription {
  subscribe(): void;
  unsubscribe(): void;
  on(event: string, handler: Function): void;
}

interface PublicationContext {
  data: any;
  info: any;
}

interface DisconnectedContext {
  code: number;
  reason: string;
  reconnect: boolean;
}

// Interface for the generic messaging client
export interface IMessagingClient {
  initialize(userId: string): Promise<void>;
  connect(): Promise<void>;
  disconnect(): void;
  subscribe(channel: string): Promise<boolean>;
  unsubscribe(channel: string): void;
  addMessageHandler(type: MessageType, handler: MessageHandler): void;
  removeMessageHandler(type: MessageType, handler: MessageHandler): void;
  isConnected(): boolean;
  getConnectionStatus(): string;
  reconnect(): void;
}

// Events that the messaging client can emit
export interface MessagingClientEvents {
  onConnected: () => void;
  onDisconnected: (reason?: string) => void;
  onConnecting: () => void;
  onError: (error: Error, context: ErrorContext) => void;
  onMessage: (channel: string, message: CentrifugoMessage) => void;
  onReconnecting: (attempt: number) => void;
}

class CentrifugoClient implements IMessagingClient {
  private centrifuge: CentrifugeClient | null = null;
  private subscriptions: Map<string, Subscription> = new Map();
  private messageHandlers: Map<MessageType, MessageHandler[]> = new Map();
  private isInitialized = false;
  private userId: string | null = null;
  private events: Partial<MessagingClientEvents> = {};
  private connectionState: 'disconnected' | 'connecting' | 'connected' =
    'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private pollingInterval: ReturnType<typeof setInterval> | null = null;
  private isPolling = false;
  private lastMessageTime = 0;
  private connectionStartTime = 0;
  private lastErrorTime = 0;
  private errorCount = 0;

  // Event registration
  on<K extends keyof MessagingClientEvents>(
    event: K,
    handler: MessagingClientEvents[K],
  ): void {
    this.events[event] = handler;
  }

  async initialize(userId: string): Promise<void> {
    if (this.isInitialized) {
      console.log('CentrifugoClient already initialized');
      return;
    }

    this.userId = userId;

    try {
      console.log('Initializing CentrifugoClient for user:', userId);
      console.log('Centrifugo URL:', config.api.websocket.centrifugo.url);
      console.log('Environment:', "config.ENVIRONMENT");
      console.log('Debug mode:', config.api.websocket.centrifugo.debug);

      const token = await this.getConnectionToken(userId);

      console.log(
        'Creating Centrifuge client with URL:',
        config.api.websocket.centrifugo.url,
      );
      console.log('Using token length:', token ? token.length : 0);

      // Configure client options for anonymous connection
      const clientOptions: any = {
        debug: config.api.websocket.centrifugo.debug,
        websocket: WebSocket, // Use React Native's native WebSocket
      };

      // For anonymous connections, don't send token at all
      if (token && token.length > 0) {
        clientOptions.token = token;
        console.log('Using token for Centrifugo connection');
      } else {
        console.log('Using anonymous connection to Centrifugo (no token)');
        // For anonymous connections, we can optionally set a user ID in the connection
        clientOptions.data = { userId: userId };
      }

      // Configure for React Native
      this.centrifuge = new Centrifuge(config.api.websocket.centrifugo.url, clientOptions);
      this.setupEventHandlers();
      this.isInitialized = true;

      console.log('CentrifugoClient initialized successfully');
    } catch (error) {
      console.error('Failed to initialize CentrifugoClient:', error);

      // Log more detailed error information
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name,
        });
      }

      this.handleError(error as Error, {
        operation: 'initialize',
        timestamp: Date.now(),
        userId,
      });
      throw error;
    }
  }

  async connect(): Promise<void> {
    if (!this.centrifuge) {
      throw new Error('CentrifugoClient not initialized');
    }

    this.connectionStartTime = Date.now();
    console.log('🔗 Attempting to connect to Centrifugo...');
    console.log('🔗 Connection state:', this.connectionState, '-> connecting');
    console.log(
      'Connection attempt started at:',
      new Date(this.connectionStartTime).toISOString(),
    );
    this.connectionState = 'connecting';

    try {
      this.centrifuge.connect();
      console.log('Centrifuge connection attempt initiated successfully');
    } catch (connectError) {
      console.error('Connection attempt failed:', connectError);
      this.connectionState = 'disconnected';
      throw connectError;
    }
  }

  disconnect(): void {
    // Unsubscribe from all channels
    this.subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.subscriptions.clear();

    // Disconnect from Centrifugo
    if (this.centrifuge) {
      this.centrifuge.removeAllListeners();
      this.centrifuge.disconnect();
      this.centrifuge = null;
    }

    // Clear message handlers
    this.messageHandlers.clear();

    // Stop polling if active
    this.stopPolling();

    this.connectionState = 'disconnected';
    this.isInitialized = false;
    console.log('CentrifugoClient disconnected');
  }

  async subscribe(channel: string): Promise<boolean> {
    if (!this.centrifuge) {
      throw new Error('CentrifugoClient not initialized');
    }

    if (this.subscriptions.has(channel)) {
      console.log(`Already subscribed to ${channel}`);
      return true;
    }

    try {
      const subscription = this.centrifuge.newSubscription(channel);

      subscription.on('publication', (ctx: PublicationContext) => {
        this.handleMessage(channel, ctx.data);
      });

      subscription.on('subscribed', (ctx: any) => {
        console.log(`Successfully subscribed to ${channel}:`, ctx);
      });

      subscription.on('subscribing', (ctx: any) => {
        console.log(`Subscribing to ${channel}:`, ctx);
      });

      subscription.on('unsubscribed', (ctx: any) => {
        console.log(`❌ Unsubscribed from ${channel}:`, ctx);
        this.subscriptions.delete(channel);
      });

      subscription.on('error', (ctx: any) => {
        console.error(`❌ Subscription error for ${channel}:`, ctx);
        this.handleError(ctx.error || new Error('Subscription error'), {
          operation: 'subscription',
          timestamp: Date.now(),
        });
      });

      subscription.subscribe();
      this.subscriptions.set(channel, subscription);

      console.log(`Subscription created for ${channel}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to subscribe to ${channel}:`, error);
      return false;
    }
  }

  unsubscribe(channel: string): void {
    const subscription = this.subscriptions.get(channel);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(channel);
      console.log(`Unsubscribed from ${channel}`);
    }
  }

  addMessageHandler(type: MessageType, handler: MessageHandler): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  removeMessageHandler(type: MessageType, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  isConnected(): boolean {
    const centrifugeConnected =
      this.connectionState === 'connected' && this.centrifuge !== null;

    const pollingConnected =
      this.isPolling && this.connectionState === 'connected';

    const connected = centrifugeConnected || pollingConnected;

    console.log('Connection check:', {
      centrifugeConnected,
      pollingConnected,
      isPolling: this.isPolling,
      connectionState: this.connectionState,
      finalResult: connected,
    });

    return connected;
  }

  getConnectionStatus(): string {
    if (this.isPolling && this.connectionState === 'connected') {
      return 'connected-polling';
    }
    return this.connectionState;
  }

  reconnect(): void {
    if (this.centrifuge && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Manual reconnection triggered (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
      );
      this.connectionState = 'connecting';
      this.centrifuge.connect();
    } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(
        'Max reconnection attempts reached, falling back to polling',
      );
      this.startPollingFallback();
    }
  }

  private setupEventHandlers(): void {
    if (!this.centrifuge) return;

    this.centrifuge.on('connected', (ctx: any) => {
      const connectionDuration = Date.now() - this.connectionStartTime;
      console.log('Connected to Centrifugo successfully!');
      console.log('Connection context:', JSON.stringify(ctx, null, 2));
      console.log('Connection stats:', {
        duration: `${connectionDuration}ms`,
        attempt: this.reconnectAttempts + 1,
        previousErrors: this.errorCount,
      });
      console.log('Connection state: connecting -> connected');
      console.log('Reset reconnect attempts counter');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      this.errorCount = 0; // Reset error count on successful connection
      this.events.onConnected?.();
    });

    this.centrifuge.on('connecting', (ctx: any) => {
      console.log('Connecting to Centrifugo...');
      console.log('Connecting context:', JSON.stringify(ctx, null, 2));
      this.connectionState = 'connecting';
      this.events.onConnecting?.();
    });

    this.centrifuge.on('disconnected', (ctx: any) => {
      const connectionDuration = this.connectionStartTime
        ? Date.now() - this.connectionStartTime
        : 0;
      this.lastErrorTime = Date.now();
      this.errorCount++;

      console.log('❌ Disconnected from Centrifugo!');
      console.log('Disconnect details:', {
        code: ctx.code,
        reason: ctx.reason,
        reconnect: ctx.reconnect,
        attempts: this.reconnectAttempts,
        connectionDuration: `${connectionDuration}ms`,
        totalErrors: this.errorCount,
        subscriptionCount: Object.keys(this.subscriptions).length,
      });
      console.log('🔗 Connection state: connected -> disconnected');
      this.connectionState = 'disconnected';
      this.events.onDisconnected?.(ctx.reason);

      // Handle specific error codes
      if (ctx.code === 3501) {
        console.warn('Error 3501: Bad Request - This usually indicates:');
        console.warn('  - Server validation failed after connection');
        console.warn('  - Invalid subscription parameters');
        console.warn('  - Authentication token issues');
        console.warn('Attempting immediate reconnection for 3501 error...');

        // For 3501 errors, try to reconnect immediately with a short delay
        setTimeout(() => {
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(
              `Reconnecting after 3501 error (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
            );
            this.connect();
          }
        }, 1000); // 1 second delay before reconnect
        return;
      }

      if (ctx.reconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
        const backoffDelay = Math.min(
          1000 * Math.pow(2, this.reconnectAttempts),
          30000,
        ); // Exponential backoff, max 30s
        console.log(
          `Will attempt to reconnect in ${backoffDelay}ms... (attempt ${
            this.reconnectAttempts + 1
          }/${this.maxReconnectAttempts})`,
        );
        this.events.onReconnecting?.(this.reconnectAttempts);

        setTimeout(() => {
          this.reconnectAttempts++;
          this.connect();
        }, backoffDelay);
      } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error(
          'Max reconnection attempts reached, falling back to polling',
        );
        this.startPollingFallback();
      }
    });

    this.centrifuge.on('error', (ctx: any) => {
      this.lastErrorTime = Date.now();
      this.errorCount++;
      const connectionDuration = this.connectionStartTime
        ? Date.now() - this.connectionStartTime
        : 0;

      console.error('Centrifugo connection error!');
      console.error('Error context:', JSON.stringify(ctx, null, 2));
      console.error('Centrifugo URL:', config.api.websocket.centrifugo.url);
      console.error('Current connection state:', this.connectionState);
      console.error('Error stats:', {
        connectionDuration: `${connectionDuration}ms`,
        totalErrors: this.errorCount,
        subscriptions: Object.keys(this.subscriptions),
        userId: this.userId,
      });

      const error =
        ctx.error ||
        new Error(
          `Centrifugo connection error: ${ctx.message || 'Unknown error'}`,
        );
      this.handleError(error, {
        operation: 'connection',
        timestamp: Date.now(),
      });
    });
  }

  private handleMessage(channel: string, data: CentrifugoMessage): void {
    try {
      console.log(`Received message on ${channel}:`, data);

      // Validate message structure
      if (!this.isValidMessage(data)) {
        console.warn('Invalid message structure:', data);
        return;
      }

      // Emit message event
      this.events.onMessage?.(channel, data);

      // Dispatch to message handlers
      const handlers = this.messageHandlers.get(data.type) || [];
      handlers.forEach(handler => {
        try {
          handler(data, channel);
        } catch (error) {
          console.error('Error in message handler:', error);
        }
      });
    } catch (error) {
      console.error('Error handling message:', error);
      this.handleError(error as Error, {
        operation: 'handleMessage',
        timestamp: Date.now(),
      });
    }
  }

  private isValidMessage(data: any): data is CentrifugoMessage {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.type === 'string' &&
      typeof data.timestamp === 'number'
    );
  }

  private async getConnectionToken(userId: string): Promise<string> {
    try {
      console.log('Getting Centrifugo connection token for user:', userId);
      console.log('Environment:', config.environment);
      console.log('API Base URL:', config.api.http.coreService.v1.url);
      console.log(
        'Token endpoint:',
        `${config.api.http.coreService.v1.url}${config.api.http.coreService.v1.paths.centrifugoToken.path}`,
      );

      // For development, try anonymous connection first
      if (config.environment === 'development') {
        console.log('Development mode: attempting anonymous connection');
        console.log('Returning empty token for anonymous connection');
        return '';
      }

      // Try to get token from backend if endpoint exists
      try {
        console.log('Attempting to fetch token from backend...');
        const authToken = await this.getAuthToken();
        console.log('Auth token length:', authToken.length);

        const requestUrl = `${config.api.http.coreService.v1.url}${config.api.http.coreService.v1.paths.centrifugoToken.path}`;
        console.log('Making request to:', requestUrl);

        const response = await fetch(requestUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${authToken}`,
          },
          body: JSON.stringify({ userId }),
        });

        console.log('Response status:', response.status, response.statusText);

        if (response.ok) {
          const data = await response.json();
          console.log('Backend response data:', JSON.stringify(data, null, 2));
          if (data.token) {
            console.log('Successfully retrieved Centrifugo token from backend');
            console.log('Token length:', data.token.length);
            return data.token;
          } else {
            console.warn('No token field in response');
          }
        } else {
          console.warn(
            'Backend token endpoint failed:',
            response.status,
            response.statusText,
          );

          // Log response body for debugging
          const errorText = await response.text();
          console.warn('Error response body:', errorText);
        }
      } catch (networkError) {
        console.error('Network error calling token endpoint:');
        console.error(
          'Error details:',
          networkError instanceof Error ? networkError.message : networkError,
        );
        if (networkError instanceof Error && networkError.stack) {
          console.error('Stack trace:', networkError.stack);
        }
      }

      // Final fallback: anonymous connection
      console.log('Fallback: attempting anonymous connection');
      console.log('Returning empty token for anonymous connection');
      return '';
    } catch (error) {
      console.error('Critical error getting connection token:');
      console.error(
        'Error details:',
        error instanceof Error ? error.message : error,
      );
      if (error instanceof Error && error.stack) {
        console.error('Stack trace:', error.stack);
      }

      this.handleError(error as Error, {
        operation: 'getConnectionToken',
        timestamp: Date.now(),
        userId,
      });
      throw error;
    }
  }

  private async getAuthToken(): Promise<string> {
    try {
      // For development, use a simple token
      if (config.environment === 'development') {
        return 'dev-auth-token';
      }

      // TODO: Implement proper authentication when needed
      // This could be JWT token, API key, or other auth method
      return 'default-auth-token';
    } catch (error) {
      console.error('Error getting auth token:', error);
      throw error;
    }
  }

  private handleError(error: Error, context: ErrorContext): void {
    console.error('CentrifugoClient error:', {
      error: error.message,
      stack: error.stack,
      context,
      connectionState: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
    });
    this.events.onError?.(error, context);
  }

  // Polling fallback methods
  private startPollingFallback(): void {
    if (this.isPolling) return;

    console.log('Starting polling fallback for real-time updates');
    this.isPolling = true;
    this.connectionState = 'connected'; // Mark as connected for polling mode
    this.reconnectAttempts = 0; // Reset reconnect attempts

    console.log('Polling status changed:', {
      isPolling: this.isPolling,
      connectionState: this.connectionState,
      isConnected: this.isConnected(),
    });

    this.events.onConnected?.();

    // Poll for messages every 3 seconds for better responsiveness
    this.pollingInterval = setInterval(() => {
      this.pollForMessages();
    }, 3000);

    console.log(
      'Polling fallback active - checking for messages every 3 seconds',
    );
  }

  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.isPolling = false;
  }

  private async pollForMessages(): Promise<void> {
    try {
      // Get all subscribed channels
      const channels = Array.from(this.subscriptions.keys());

      for (const channel of channels) {
        await this.pollChannel(channel);
      }
    } catch (error) {
      console.error('❌ Polling error:', error);
    }
  }

  private async pollChannel(channel: string): Promise<void> {
    try {
      const url = `${config.api.http.centrifugo.v1.url}${config.api.http.centrifugo.v1.paths.admin.path}`;

      const requestBody = {
        method: 'history',
        params: {
          channel: channel,
          limit: 10,
          since: {
            offset: this.lastMessageTime,
          },
        },
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          accept: 'application/json',
          authorization: `token ${config.api.http.centrifugo.v1.paths.admin.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const data = await response.json();
        const publications = data?.result?.publications || [];

        // Process new messages
        publications.forEach((pub: any) => {
          if (
            pub.info?.timestamp &&
            pub.info.timestamp > this.lastMessageTime
          ) {
            console.log(`Polled message from ${channel}:`, pub.data);
            this.handleMessage(channel, pub.data);
            this.lastMessageTime = pub.info.timestamp;
          }
        });
      }
    } catch (error) {
      console.warn(`Failed to poll channel ${channel}:`, error);
    }
  }

  // Utility method to get user channels
  getUserChannels(userId: string): string[] {
    return [];
  }

  // Batch subscription method
  async subscribeToMultiple(channels: string[]): Promise<string[]> {
    const successful: string[] = [];
    const failed: string[] = [];

    const subscriptionPromises = channels.map(async channel => {
      try {
        const success = await this.subscribe(channel);
        if (success) {
          successful.push(channel);
        } else {
          failed.push(channel);
        }
      } catch (error) {
        console.error(`Failed to subscribe to ${channel}:`, error);
        failed.push(channel);
      }
    });

    await Promise.allSettled(subscriptionPromises);

    if (failed.length > 0) {
      console.warn('Failed to subscribe to channels:', failed);
    }

    return successful;
  }
}

export default CentrifugoClient;
