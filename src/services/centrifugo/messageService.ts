import { useAppStore } from '../../stores/useAppStore';
import CentrifugoClient, {
  IMessagingClient,
  MessagingClientEvents,
} from './CentrifugoClient';
import {
  CentrifugoMessage,
  MessageType,
  MessageHandler,
  PendingRequest,
  ErrorContext,
} from '../../types';
import config from '../../config';

class MessageService {
  private static instance: MessageService;
  private client: IMessagingClient;
  private isInitialized = false;
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private heartbeatTimer: ReturnType<typeof setTimeout> | null = null;
  private reconnectAttempts = 0;

  private constructor() {
    this.client = new CentrifugoClient();
    this.setupClientEventHandlers();
  }

  static getInstance(): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService();
    }
    return MessageService.instance;
  }

  async initialize(userId: string): Promise<void> {
    if (this.isInitialized) {
      console.log('MessageService already initialized');
      return;
    }

    try {
      // Initialize the underlying client
      await this.client.initialize(userId);

      // Connect to the service
      await this.client.connect();

      // Subscribe to user channels
      await this.subscribeToUserChannels(userId);

      this.isInitialized = true;
      this.startHeartbeat();

      console.log('MessageService initialized successfully');
    } catch (error) {
      console.warn(
        'Failed to initialize MessageService, continuing without real-time features:',
        error,
      );
      this.handleError(error as Error, {
        operation: 'initialize',
        timestamp: Date.now(),
        userId,
      });
      // Set as initialized to prevent blocking app startup
      this.isInitialized = true;
    }
  }

  private setupClientEventHandlers(): void {
    const events: MessagingClientEvents = {
      onConnected: () => {
        console.log('MessageService: Connected');
        useAppStore.getState().updateRealtimeStatus('connected');
        useAppStore.getState().resetReconnectAttempts();
        this.reconnectAttempts = 0;
        this.clearReconnectTimer();
      },

      onConnecting: () => {
        console.log('MessageService: Connecting');
        useAppStore.getState().updateRealtimeStatus('connecting');
      },

      onDisconnected: reason => {
        console.log('MessageService: Disconnected', reason);
        useAppStore.getState().updateRealtimeStatus('disconnected');
        this.stopHeartbeat();
        this.handleReconnection();
      },

      onError: (error, context) => {
        console.error('MessageService: Client error', error, context);
        useAppStore.getState().updateRealtimeStatus('error');
        this.handleError(error, context);
      },

      onMessage: (channel, message) => {
        this.handleMessage(channel, message);
      },

      onReconnecting: attempt => {
        console.log(`MessageService: Reconnecting (attempt ${attempt})`);
        this.reconnectAttempts = attempt;
      },
    };

    // Register event handlers
    Object.entries(events).forEach(([event, handler]) => {
      (this.client as any).on(event, handler);
    });
  }

  private handleMessage(channel: string, message: CentrifugoMessage): void {
    try {
      console.log(`MessageService: Handling message on ${channel}:`, message);

      // Handle pending requests
      if (message.requestId) {
        this.resolvePendingRequest(message.requestId, message.data);
      }

      // Update store based on message type
      this.updateStoreFromMessage(message);
    } catch (error) {
      console.error('Error handling message in MessageService:', error);
      this.handleError(error as Error, {
        operation: 'handleMessage',
        timestamp: Date.now(),
      });
    }
  }

  private resolvePendingRequest(requestId: string, data: any): void {
    const store = useAppStore.getState();
    const pendingRequest = store.realtime.pendingRequests.get(requestId);

    if (pendingRequest) {
      clearTimeout(pendingRequest.timeout);
      pendingRequest.resolve(data);
      store.removePendingRequest(requestId);
      console.log(`Resolved pending request: ${requestId}`);
    }
  }

  private updateStoreFromMessage(message: CentrifugoMessage): void {
    const store = useAppStore.getState();

    switch (message.type) {
      case MessageType.OBJECTIVES_UPDATED:
        store.setObjectives(message.data);
        break;

      case MessageType.QUESTS_UPDATED:
        store.setTodayQuests(message.data);
        break;

      case MessageType.QUEST_COMPLETED:
        store.updateQuestStatus(message.data.questId, 'COMPLETED');
        if (message.data.xpGained) {
          store.updateUserXP(message.data.xpGained);
        }
        store.showToast(
          `🎉 Quest completed! +${message.data.xpGained || 0} XP`,
          'success',
        );
        break;

      case MessageType.LEVEL_UP:
        store.showLevelUpModal(message.data);
        store.showToast(
          `🎊 Level Up! Welcome to Level ${message.data.newLevel}`,
          'success',
          5000,
        );
        break;

      case MessageType.XP_GAINED:
        store.updateUserXP(message.data.xpAmount);
        break;

      case MessageType.CHAT_MESSAGE:
        store.addChatMessage(message.data);
        break;

      case MessageType.CHAT_PREVIEW_CARD:
        store.addPendingPreview(message.data);
        break;

      case MessageType.NOTIFICATION_RECEIVED:
        store.addNotification(message.data);
        store.showToast(message.data.title, 'info');
        break;

      case MessageType.ERROR_OCCURRED:
        console.error('Server error:', message.data);
        store.showToast(message.data.message || 'An error occurred', 'error');
        break;

      case MessageType.RATE_LIMITED:
        const retryAfter = message.data.retryAfter || 60;
        store.showToast(
          `Rate limited. Please wait ${retryAfter} seconds.`,
          'warning',
        );
        break;

      default:
        console.log('Unhandled message type:', message.type);
    }
  }

  // Public API methods
  addMessageHandler(type: MessageType, handler: MessageHandler): void {
    this.client.addMessageHandler(type, handler);
  }

  removeMessageHandler(type: MessageType, handler: MessageHandler): void {
    this.client.removeMessageHandler(type, handler);
  }

  async subscribe(channel: string): Promise<boolean> {
    return await this.client.subscribe(channel);
  }

  unsubscribe(channel: string): void {
    this.client.unsubscribe(channel);
  }

  reconnect(): void {
    console.log('MessageService: Manual reconnection triggered');
    this.client.reconnect();
  }

  disconnect(): void {
    this.clearReconnectTimer();
    this.stopHeartbeat();

    // Disconnect the underlying client
    this.client.disconnect();

    this.isInitialized = false;
    useAppStore.getState().updateRealtimeStatus('disconnected');

    console.log('MessageService disconnected');
  }

  getConnectionStatus(): string {
    return this.client.getConnectionStatus();
  }

  isConnected(): boolean {
    return this.client.isConnected();
  }

  async waitForRealtimeResponse<T>(requestId: string): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        useAppStore.getState().removePendingRequest(requestId);
        reject(new Error('Request timeout'));
      }, config.api.http.coreService.v1.requestTimeout);

      const request: PendingRequest = {
        requestId,
        type: 'api_request',
        timestamp: Date.now(),
        timeout,
        resolve,
        reject,
      };

      useAppStore.getState().addPendingRequest(request);
    });
  }

  // Private methods for connection management
  private handleReconnection(): void {
    const store = useAppStore.getState();
    const maxAttempts = config.api.websocket.centrifugo.maxReconnectAttempts;

    if (this.reconnectAttempts >= maxAttempts) {
      console.error('Max reconnection attempts reached');
      store.updateRealtimeStatus('error');
      store.showToast('Connection lost. Please refresh the app.', 'error');
      return;
    }

    this.reconnectAttempts++;
    store.incrementReconnectAttempts();

    const delay = Math.min(
      config.api.websocket.centrifugo.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000, // Max 30 seconds
    );

    console.log(
      `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${maxAttempts})`,
    );

    this.reconnectTimer = setTimeout(() => {
      try {
        this.client.reconnect();
      } catch (error) {
        console.error('Reconnection failed:', error);
        this.handleReconnection(); // Try again
      }
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      useAppStore.getState().updateLastHeartbeat();
    }, 30000); // Update heartbeat every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private handleError(error: Error, context: ErrorContext): void {
    console.error('MessageService error:', error, context);

    // Log to analytics/monitoring service
    if (typeof error === 'object' && error.message) {
      useAppStore
        .getState()
        .showToast('Connection issue. Retrying...', 'warning');
    }
  }

  // Utility methods
  private async subscribeToUserChannels(userId: string): Promise<void> {
    console.log('User channel subscriptions skipped');
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      return this.isConnected() && this.isInitialized;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  // Statistics
  getConnectionStats(): {
    isInitialized: boolean;
    isConnected: boolean;
    reconnectAttempts: number;
    connectionStatus: string;
  } {
    return {
      isInitialized: this.isInitialized,
      isConnected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      connectionStatus: this.getConnectionStatus(),
    };
  }

  // Council chat specific methods
  generateCouncilChannelId(userId: string, characterId: string): string {
    return `chat:${userId}:${characterId}`;
  }

  async subscribeToCouncilChannel(
    userId: string,
    characterId: string,
  ): Promise<boolean> {
    const channel = this.generateCouncilChannelId(userId, characterId);
    try {
      const success = await this.client.subscribe(channel);
      if (success) {
        console.log(`Successfully subscribed to council channel: ${channel}`);
      }
      return success;
    } catch (error) {
      console.warn(
        `Failed to subscribe to council channel ${channel}, real-time updates may not work:`,
        error,
      );
      return false;
    }
  }

  unsubscribeFromCouncilChannel(userId: string, characterId: string): void {
    const channel = this.generateCouncilChannelId(userId, characterId);
    try {
      this.client.unsubscribe(channel);
      console.log(`Unsubscribed from council channel: ${channel}`);
    } catch (error) {
      console.warn(
        `Failed to unsubscribe from council channel ${channel}:`,
        error,
      );
    }
  }

  addCouncilMessageHandler(handler: MessageHandler): void {
    try {
      this.client.addMessageHandler(MessageType.CHAT_MESSAGE, handler);
    } catch (error) {
      console.warn('Failed to add council message handler:', error);
    }
  }

  removeCouncilMessageHandler(handler: MessageHandler): void {
    try {
      this.client.removeMessageHandler(MessageType.CHAT_MESSAGE, handler);
    } catch (error) {
      console.warn('Failed to remove council message handler:', error);
    }
  }
}

export default MessageService;
