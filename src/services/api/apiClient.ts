import config from '../../config';
import MessageService from '../centrifugo/MessageService';
import { ApiResponse, RequestOptions, APIError, ErrorType } from '../../types';
import { firebase } from '@react-native-firebase/auth';

class ApiClient {
  private baseURL: string;
  private messageService: MessageService;

  constructor() {
    this.baseURL = config.api.http.coreService.v1.url;
    this.messageService = MessageService.getInstance();
  }

  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    try {
      const url = `${this.baseURL}${endpoint}`;

      const requestOptions: RequestInit = {
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${await this.getAuthToken()}`,
          ...options.headers,
        },
        ...options,
      };

      if (config.featureFlags.indexOf('mockApiResponses') >= 0) {
        return this.getMockResponse<T>(endpoint, options);
      }

      console.log(`API Request: ${requestOptions.method} ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        config.api.http.coreService.v1.requestTimeout,
      );

      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const result: ApiResponse<T> = await response.json();

      if (!response.ok) {
        throw new APIError(
          this.mapHttpStatusToErrorType(response.status),
          result.message || 'Request failed',
          response.status.toString(),
          result,
          new Date(),
        );
      }

      // For real-time operations, wait for data via Centrifugo
      if (result.requestId && !result.data) {
        console.log(
          `Waiting for real-time response for request: ${result.requestId}`,
        );
        return this.messageService.waitForRealtimeResponse<T>(result.requestId);
      }

      return result.data || (result as T);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private async waitForRealtimeResponse<T>(requestId: string): Promise<T> {
    try {
      return await this.messageService.waitForRealtimeResponse<T>(requestId);
    } catch (error) {
      console.error(`Real-time response timeout for request: ${requestId}`);
      throw new APIError(
        ErrorType.EXTERNAL_SERVICE_ERROR,
        'Request timed out waiting for server response',
        'TIMEOUT',
        { requestId },
        new Date(),
      );
    }
  }

  private async getAuthToken(): Promise<string> {
    try {
      // In a real implementation, get this from Firebase Auth
      // For now, return a mock token or implement Firebase auth integration
      if (config.featureFlags.indexOf('mockApiResponses') >= 0) {
        return 'mock-auth-token';
      }

      const user = firebase.auth().currentUser;
      if (user) {
        return await user.getIdToken();
      }

      throw new Error('No authenticated user');
    } catch (error) {
      console.error('Failed to get auth token:', error);
      throw new APIError(
        ErrorType.AUTHENTICATION_ERROR,
        'Authentication required',
        'AUTH_REQUIRED',
        error,
        new Date(),
      );
    }
  }

  private mapHttpStatusToErrorType(status: number): ErrorType {
    switch (status) {
      case 400:
        return ErrorType.VALIDATION_ERROR;
      case 401:
        return ErrorType.AUTHENTICATION_ERROR;
      case 403:
        return ErrorType.AUTHORIZATION_ERROR;
      case 404:
        return ErrorType.NOT_FOUND;
      case 429:
        return ErrorType.RATE_LIMIT_EXCEEDED;
      case 402:
      case 451: // Subscription required (custom status)
        return ErrorType.SUBSCRIPTION_REQUIRED;
      case 500:
      case 502:
      case 503:
      case 504:
        return ErrorType.INTERNAL_SERVER_ERROR;
      default:
        return ErrorType.EXTERNAL_SERVICE_ERROR;
    }
  }

  private handleError(error: any): Error {
    if (error instanceof APIError) {
      return error;
    }

    if (error.name === 'AbortError') {
      return new APIError(
        ErrorType.EXTERNAL_SERVICE_ERROR,
        'Request was cancelled',
        'REQUEST_CANCELLED',
        error,
        new Date(),
      );
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new APIError(
        ErrorType.EXTERNAL_SERVICE_ERROR,
        'Network error. Please check your connection.',
        'NETWORK_ERROR',
        error,
        new Date(),
      );
    }

    return new APIError(
      ErrorType.EXTERNAL_SERVICE_ERROR,
      error.message || 'An unexpected error occurred',
      'UNKNOWN_ERROR',
      error,
      new Date(),
    );
  }

  private getMockResponse<T>(endpoint: string, options: RequestOptions): T {
    console.log(
      `Mock API Response for: ${options.method || 'GET'} ${endpoint}`,
    );

    // Return mock data based on endpoint
    const mockResponses: Record<string, any> = {
      '/api/objectives': [
        {
          id: 'obj-1',
          name: 'Lose 10kg in 6 months',
          description: 'Get fit and healthy',
          status: 'ACTIVE',
          totalXP: 1000,
          earnedXP: 350,
          difficulty: 'MEDIUM',
        },
      ],
      '/api/quests/today': [
        {
          id: 'quest-1',
          name: 'Morning Workout',
          description: '30 minutes cardio exercise',
          type: 'NORMAL',
          difficulty: 'MEDIUM',
          xpReward: 25,
          scheduledTime: '08:00',
          status: 'PENDING',
        },
        {
          id: 'quest-2',
          name: 'Healthy Breakfast',
          description: 'Eat a protein-rich breakfast',
          type: 'NORMAL',
          difficulty: 'EASY',
          xpReward: 10,
          scheduledTime: '09:00',
          status: 'PENDING',
        },
      ],
      '/api/chat/messages': [
        {
          id: 'msg-1',
          type: 'GM_TEXT',
          content:
            'Great job on completing your morning workout! Ready for the next challenge?',
          timestamp: new Date().toISOString(),
        },
      ],
      '/api/users/profile': {
        id: 'user-1',
        displayName: 'Test User',
        alias: 'Champion',
        email: '<EMAIL>',
      },
    };

    // For POST requests, simulate immediate response with requestId
    if (options.method === 'POST') {
      return {
        success: true,
        requestId: `req-${Date.now()}`,
        message: 'Request accepted',
      } as T;
    }

    return mockResponses[endpoint] || ({ success: true } as T);
  }

  // Convenience methods for different HTTP methods
  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', headers });
  }

  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>,
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>,
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async delete<T>(
    endpoint: string,
    headers?: Record<string, string>,
  ): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', headers });
  }

  // Upload files
  async uploadFile<T>(
    endpoint: string,
    file: FormData,
    onProgress?: (progress: number) => void,
  ): Promise<T> {
    try {
      const xhr = new XMLHttpRequest();

      return new Promise<T>((resolve, reject) => {
        xhr.upload.onprogress = event => {
          if (event.lengthComputable && onProgress) {
            const progress = Math.round((event.loaded * 100) / event.total);
            onProgress(progress);
          }
        };

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response.data || response);
            } catch (error) {
              reject(new Error('Invalid JSON response'));
            }
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`));
          }
        };

        xhr.onerror = () => {
          reject(new Error('Upload failed'));
        };

        xhr.open('POST', `${this.baseURL}${endpoint}`);
        xhr.setRequestHeader('Authorization', `Bearer ${this.getAuthToken()}`);
        xhr.send(file);
      });
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: number }> {
    try {
      return await this.get('/health');
    } catch (error) {
      return {
        status: 'error',
        timestamp: Date.now(),
      };
    }
  }
}

export default ApiClient;

// Export a singleton instance
export const apiClient = new ApiClient();
