import { apiClient } from './apiClient';
import config from '../../config';

class ChatService {
  async sendMessage(
    messageText: string,
    userId: string = config.defaults.userId,
    characterId: string = config.defaults.characterId,
    objectiveId: string = config.defaults.objectiveId,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const messagePayload = {
        userId,
        characterId,
        objectiveId,
        messageType: 'SIMPLE',
        message: {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          parentId: null,
          text: messageText,
          language: 'en',
          mentionedUserIds: null,
          chatRole: 'USER',
        },
      };

      await apiClient.request(
        `${config.api.http.coreService.v1.paths.sendCouncilMessage.path}`,
        {
          method: 'POST',
          headers: {
            accept: '/',
            idempotencyKey: `${Date.now()}-${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(messagePayload),
        },
      );

      return { success: true };
    } catch (error) {
      console.error('Error sending chat message:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async getHistory(
    userId: string = config.defaults.userId,
    characterId: string = config.defaults.characterId,
    limit: number = 50,
  ): Promise<{ success: boolean; messages?: any[]; error?: string }> {
    try {
      const channel = `chat:${userId}:${characterId}`;
      const url = `${config.api.http.centrifugo.v1.url}${config.api.http.centrifugo.v1.paths.admin}`;

      const requestBody = {
        method: 'history',
        params: {
          channel: channel,
          limit: limit,
          reverse: false,
        },
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          accept: 'application/json',
          authorization: `token ${config.api.http.centrifugo.v1.paths.admin.token}`,
          'Content-Type': 'text/plain;charset=UTF-8',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          'Centrifugo history API error:',
          response.status,
          errorText,
        );
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`,
        };
      }

      const data = await response.json();
      const publications = data?.result?.publications || [];

      // Transform Centrifugo publications to our message format
      const messages = publications.map((pub: any) => ({
        id:
          pub.data?.message?.id ||
          `history-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        text: pub.data?.message?.text || pub.data?.text || '',
        user: pub.data?.message?.chatRole === 'USER' ? 'You' : 'Assistant',
        timestamp: pub.info?.timestamp ? pub.info.timestamp * 1000 : Date.now(),
        type: pub.data?.message?.chatRole === 'USER' ? 'sent' : 'received',
        characterId: pub.data?.characterId || characterId,
        objectiveId:
          pub.data?.objectiveId || config.defaults.objectiveId,
      }));

      return {
        success: true,
        messages: messages,
      };
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }
}

export default ChatService;
export const chatService = new ChatService();
