import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { persist, createSelectivePersist } from './middleware/persistMiddleware';
import {
  AppState,
  User,
  ObjectiveJourney,
  Quest,
  ChatMessage,
  Toast,
  PendingRequest,
  ObjectiveFilters,
  UserStats,
  PreviewCard,
} from '../types';

interface AppActions {
  // Auth actions
  setAuthenticated: (isAuthenticated: boolean) => void;
  setUser: (user: User | null) => void;
  setAuthLoading: (loading: boolean) => void;
  setAuthError: (error: string | null) => void;
  logout: () => void;

  // User actions
  updateUserProfile: (updates: Partial<User>) => void;
  updateUserStats: (stats: Partial<UserStats>) => void;
  updateUserXP: (xpGained: number) => void;

  // Objectives actions
  setObjectives: (objectives: ObjectiveJourney[]) => void;
  addObjective: (objective: ObjectiveJourney) => void;
  updateObjective: (id: string, updates: Partial<ObjectiveJourney>) => void;
  setCurrentObjective: (objective: ObjectiveJourney | null) => void;
  setObjectiveFilters: (filters: Partial<ObjectiveFilters>) => void;
  setObjectivesLoading: (loading: boolean) => void;

  // Quests actions
  setTodayQuests: (quests: Quest[]) => void;
  setAllQuests: (quests: Quest[]) => void;
  updateQuestStatus: (questId: string, status: Quest['status']) => void;
  addQuest: (quest: Quest) => void;
  setQuestCompleting: (questId: string, isCompleting: boolean) => void;
  setQuestsLoading: (loading: boolean) => void;
  cleanupOldQuests: (daysOld: number) => void;

  // Chat actions
  addChatMessage: (message: ChatMessage) => void;
  addOptimisticMessage: (message: Partial<ChatMessage>) => void;
  setChatLoading: (loading: boolean) => void;
  setChatTyping: (typing: boolean) => void;
  addPendingPreview: (preview: PreviewCard) => void;
  removePendingPreview: (previewId: string) => void;
  trimChatMessages: (keepCount: number) => void;

  // UI actions
  setTheme: (theme: 'light' | 'dark') => void;
  setActiveTab: (tab: string) => void;
  showModal: (modalName: keyof AppState['ui']['modals']) => void;
  hideModal: (modalName: keyof AppState['ui']['modals']) => void;
  showToast: (message: string, type?: Toast['type'], duration?: number) => void;
  hideToast: (toastId: string) => void;
  showLevelUpModal: (data: any) => void;

  // Realtime actions
  updateRealtimeStatus: (
    status: AppState['realtime']['connectionStatus'],
  ) => void;
  setRealtimeConnected: (connected: boolean) => void;
  addPendingRequest: (request: PendingRequest) => void;
  removePendingRequest: (requestId: string) => void;
  updateLastHeartbeat: () => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;

  // Navigation actions
  setCurrentRoute: (route: string) => void;

  // Utility actions
  setOfflineMode: (offline: boolean) => void;
  addNotification: (notification: any) => void;
  cleanupOldNotifications: (daysOld: number) => void;
}

type StoreState = AppState & AppActions;

export const useAppStore = create<StoreState>()(
  subscribeWithSelector(
    persist(
      (set, _get) => ({
    // Initial State
    auth: {
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    },

    user: {
      profile: null,
      stats: {
        totalXP: 0,
        completedQuests: 0,
        activeObjectives: 0,
        streak: 0,
        level: 1,
      },
      loading: false,
    },

    objectives: {
      objectives: [],
      currentObjective: null,
      loading: false,
      filters: {
        status: 'all',
        dateRange: 'all',
      },
    },

    quests: {
      todayQuests: [],
      allQuests: [],
      loading: false,
      completingQuests: new Set(),
    },

    chat: {
      messages: [],
      loading: false,
      typing: false,
      pendingPreviews: [],
    },
    ui: {
      theme: {
        name: 'light',
        journeyTheme: {
          name: 'default',
          colors: {
            primary: '#6366F1',
            secondary: '#8B5CF6',
            accent: '#10B981',
            background: '#FFFFFF',
            error: '#EF4444',
            success: '#10B981',
            warning: '#F59E0B',
            text: '#111827',
            textSecondary: '#6B7280',
            border: '#E5E7EB',
            disabled: '#9CA3AF',
          },
          typography: {
            fontFamily: 'System',
            fontSizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 24 },
            lineHeights: { xs: 16, sm: 20, md: 24, lg: 28, xl: 32 },
          },
          buttons: {
            default: {
              backgroundColor: '#F3F4F6',
              textColor: '#374151',
              borderRadius: 8,
              paddingVertical: 12,
              paddingHorizontal: 16,
              elevation: 2,
            },
            primary: { backgroundColor: '#6366F1', textColor: '#FFFFFF' },
            secondary: { backgroundColor: '#8B5CF6', textColor: '#FFFFFF' },
            disabled: { backgroundColor: '#9CA3AF', textColor: '#6B7280' },
          },
          inputs: {
            backgroundColor: '#F9FAFB',
            borderColor: '#D1D5DB',
            textColor: '#111827',
            placeholderColor: '#6B7280',
            focusBorderColor: '#6366F1',
            errorBorderColor: '#EF4444',
            borderRadius: 8,
            padding: 12,
          },
          cards: {
            backgroundColor: '#FFFFFF',
            borderRadius: 12,
            shadowColor: '#000000',
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
            padding: 16,
          },
          animations: {
            duration: { fast: 200, normal: 300, slow: 500 },
            easing: {
              linear: (value: number) => value,
              easeIn: (value: number) => value * value,
              easeOut: (value: number) => 1 - Math.pow(1 - value, 2),
              easeInOut: (value: number) =>
                value < 0.5
                  ? 2 * value * value
                  : 1 - Math.pow(-2 * value + 2, 2) / 2,
            },
            transitions: {
              fadeIn: 300,
              fadeOut: 200,
              slideIn: 400,
              slideOut: 300,
            },
          },
          icons: {
            size: { sm: 16, md: 24, lg: 32 },
            color: '#6B7280',
          },
          activeTab: 'objectives',
          modals: { gameMaster: false, subscription: false },
          toasts: [],
        },
      },
      activeTab: 'objectives',
      modals: {
        profile: false,
        gameMaster: false,
        subscription: false,
      },
      toasts: [],
    },

    navigation: {
      currentRoute: '',
      previousRoutes: [],
    },

    realtime: {
      isConnected: false,
      connectionStatus: 'disconnected',
      pendingRequests: new Map(),
      lastHeartbeat: 0,
      reconnectAttempts: 0,
    },

    // Auth Actions
    setAuthenticated: (isAuthenticated: boolean) =>
      set(state => ({
        auth: { ...state.auth, isAuthenticated },
      })),

    setUser: (user: User | null) =>
      set(state => ({
        auth: { ...state.auth, user },
        user: { ...state.user, profile: user },
      })),

    setAuthLoading: (loading: boolean) =>
      set(state => ({
        auth: { ...state.auth, loading },
      })),

    setAuthError: (error: string | null) =>
      set(state => ({
        auth: { ...state.auth, error },
      })),

    logout: () =>
      set(() => ({
        auth: {
          isAuthenticated: false,
          user: null,
          loading: false,
          error: null,
        },
        user: {
          profile: null,
          stats: {
            totalXP: 0,
            completedQuests: 0,
            activeObjectives: 0,
            streak: 0,
            level: 1,
          },
          loading: false,
        },
        objectives: {
          objectives: [],
          currentObjective: null,
          loading: false,
          filters: {
            status: 'all',
            dateRange: 'all',
          },
        },
        quests: {
          todayQuests: [],
          allQuests: [],
          loading: false,
          completingQuests: new Set(),
        },
        chat: {
          messages: [],
          loading: false,
          typing: false,
          pendingPreviews: [],
        },
        realtime: {
          isConnected: false,
          connectionStatus: 'disconnected',
          pendingRequests: new Map(),
          lastHeartbeat: 0,
          reconnectAttempts: 0,
        },
      })),

    // User Actions
    updateUserProfile: (updates: Partial<User>) =>
      set(state => ({
        auth: {
          ...state.auth,
          user: state.auth.user ? { ...state.auth.user, ...updates } : null,
        },
        user: {
          ...state.user,
          profile: state.user.profile
            ? { ...state.user.profile, ...updates }
            : null,
        },
      })),

    updateUserStats: (stats: Partial<UserStats>) =>
      set(state => ({
        user: {
          ...state.user,
          stats: { ...state.user.stats, ...stats },
        },
      })),

    updateUserXP: (xpGained: number) =>
      set(state => {
        const newTotalXP = state.user.stats.totalXP + xpGained;
        const newLevel = Math.floor(newTotalXP / 100) + 1; // Level up every 100 XP

        return {
          user: {
            ...state.user,
            stats: {
              ...state.user.stats,
              totalXP: newTotalXP,
              level: newLevel,
            },
          },
        };
      }),

    // Objectives Actions
    setObjectives: (objectives: ObjectiveJourney[]) =>
      set(state => ({
        objectives: { ...state.objectives, objectives, loading: false },
      })),

    addObjective: (objective: ObjectiveJourney) =>
      set(state => ({
        objectives: {
          ...state.objectives,
          objectives: [...state.objectives.objectives, objective],
        },
      })),

    updateObjective: (id: string, updates: Partial<ObjectiveJourney>) =>
      set(state => ({
        objectives: {
          ...state.objectives,
          objectives: state.objectives.objectives.map(obj =>
            obj.id === id ? { ...obj, ...updates } : obj,
          ),
        },
      })),

    setCurrentObjective: (objective: ObjectiveJourney | null) =>
      set(state => ({
        objectives: { ...state.objectives, currentObjective: objective },
      })),

    setObjectiveFilters: (filters: Partial<ObjectiveFilters>) =>
      set(state => ({
        objectives: {
          ...state.objectives,
          filters: { ...state.objectives.filters, ...filters },
        },
      })),

    setObjectivesLoading: (loading: boolean) =>
      set(state => ({
        objectives: { ...state.objectives, loading },
      })),

    // Quests Actions
    setTodayQuests: (quests: Quest[]) =>
      set(state => ({
        quests: { ...state.quests, todayQuests: quests, loading: false },
      })),

    setAllQuests: (quests: Quest[]) =>
      set(state => ({
        quests: { ...state.quests, allQuests: quests },
      })),

    updateQuestStatus: (questId: string, status: Quest['status']) =>
      set(state => {
        const updateQuest = (quest: Quest) =>
          quest.id === questId
            ? {
                ...quest,
                status,
                completedAt:
                  status === 'COMPLETED' ? new Date() : quest.completedAt,
              }
            : quest;

        return {
          quests: {
            ...state.quests,
            todayQuests: state.quests.todayQuests.map(updateQuest),
            allQuests: state.quests.allQuests.map(updateQuest),
            completingQuests: new Set(
              [...state.quests.completingQuests].filter(id => id !== questId),
            ),
          },
          user:
            status === 'COMPLETED'
              ? {
                  ...state.user,
                  stats: {
                    ...state.user.stats,
                    completedQuests: state.user.stats.completedQuests + 1,
                  },
                }
              : state.user,
        };
      }),

    addQuest: (quest: Quest) =>
      set(state => ({
        quests: {
          ...state.quests,
          allQuests: [...state.quests.allQuests, quest],
          todayQuests:
            quest.scheduledDate.toDateString() === new Date().toDateString()
              ? [...state.quests.todayQuests, quest]
              : state.quests.todayQuests,
        },
      })),

    setQuestCompleting: (questId: string, isCompleting: boolean) =>
      set(state => {
        const completingQuests = new Set(state.quests.completingQuests);
        if (isCompleting) {
          completingQuests.add(questId);
        } else {
          completingQuests.delete(questId);
        }

        return {
          quests: { ...state.quests, completingQuests },
        };
      }),

    setQuestsLoading: (loading: boolean) =>
      set(state => ({
        quests: { ...state.quests, loading },
      })),

    cleanupOldQuests: (daysOld: number) =>
      set(state => {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);

        return {
          quests: {
            ...state.quests,
            allQuests: state.quests.allQuests.filter(
              quest =>
                quest.status !== 'COMPLETED' || quest.completedAt! > cutoffDate,
            ),
          },
        };
      }),

    // Chat Actions
    addChatMessage: (message: ChatMessage) =>
      set(state => ({
        chat: {
          ...state.chat,
          messages: [...state.chat.messages, message],
          typing: false,
        },
      })),

    addOptimisticMessage: (message: Partial<ChatMessage>) =>
      set(state => ({
        chat: {
          ...state.chat,
          messages: [
            ...state.chat.messages,
            {
              id: `temp-${Date.now()}`,
              userId: state.auth.user?.id || '',
              gameMasterId: '',
              type: 'USER_TEXT',
              content: '',
              timestamp: new Date(),
              isRead: false,
              ...message,
            } as ChatMessage,
          ],
        },
      })),

    setChatLoading: (loading: boolean) =>
      set(state => ({
        chat: { ...state.chat, loading },
      })),

    setChatTyping: (typing: boolean) =>
      set(state => ({
        chat: { ...state.chat, typing },
      })),

    addPendingPreview: (preview: PreviewCard) =>
      set(state => ({
        chat: {
          ...state.chat,
          pendingPreviews: [...state.chat.pendingPreviews, preview],
        },
      })),

    removePendingPreview: (previewId: string) =>
      set(state => ({
        chat: {
          ...state.chat,
          pendingPreviews: state.chat.pendingPreviews.filter(
            p => p.id !== previewId,
          ),
        },
      })),

    trimChatMessages: (keepCount: number) =>
      set(state => ({
        chat: {
          ...state.chat,
          messages: state.chat.messages.slice(-keepCount),
        },
      })),

    // UI Actions
    setTheme: (theme: 'light' | 'dark') =>
      set(state => ({
        ui: { ...state.ui, theme: theme as any },
      })),

    setActiveTab: (activeTab: string) =>
      set(state => ({
        ui: { ...state.ui, activeTab: activeTab as any },
      })),

    showModal: (modalName: keyof AppState['ui']['modals']) =>
      set(state => ({
        ui: {
          ...state.ui,
          modals: { ...state.ui.modals, [modalName]: true },
        },
      })),

    hideModal: (modalName: keyof AppState['ui']['modals']) =>
      set(state => ({
        ui: {
          ...state.ui,
          modals: { ...state.ui.modals, [modalName]: false },
        },
      })),

    showToast: (
      message: string,
      type: Toast['type'] = 'info',
      duration = 3000,
    ) =>
      set(state => {
        const toast: Toast = {
          id: `toast-${Date.now()}`,
          message,
          type,
          duration,
        };

        return {
          ui: {
            ...state.ui,
            toasts: [...state.ui.toasts, toast],
          },
        };
      }),

    hideToast: (toastId: string) =>
      set(state => ({
        ui: {
          ...state.ui,
          toasts: state.ui.toasts.filter(toast => toast.id !== toastId),
        },
      })),

    showLevelUpModal: (_data: any) =>
      set(state => ({
        ui: {
          ...state.ui,
          modals: { ...state.ui.modals, levelUp: true },
        },
      })),

    // Realtime Actions
    updateRealtimeStatus: (
      connectionStatus: AppState['realtime']['connectionStatus'],
    ) =>
      set(state => ({
        realtime: {
          ...state.realtime,
          connectionStatus,
          isConnected: connectionStatus === 'connected',
        },
      })),

    setRealtimeConnected: (isConnected: boolean) =>
      set(state => ({
        realtime: {
          ...state.realtime,
          isConnected,
          connectionStatus: isConnected ? 'connected' : 'disconnected',
        },
      })),

    addPendingRequest: (request: PendingRequest) =>
      set(state => {
        const newMap = new Map(state.realtime.pendingRequests);
        newMap.set(request.requestId, request);

        return {
          realtime: { ...state.realtime, pendingRequests: newMap },
        };
      }),

    removePendingRequest: (requestId: string) =>
      set(state => {
        const newMap = new Map(state.realtime.pendingRequests);
        const request = newMap.get(requestId);
        if (request) {
          clearTimeout(request.timeout);
          newMap.delete(requestId);
        }

        return {
          realtime: { ...state.realtime, pendingRequests: newMap },
        };
      }),

    updateLastHeartbeat: () =>
      set(state => ({
        realtime: { ...state.realtime, lastHeartbeat: Date.now() },
      })),

    incrementReconnectAttempts: () =>
      set(state => ({
        realtime: {
          ...state.realtime,
          reconnectAttempts: state.realtime.reconnectAttempts + 1,
        },
      })),

    resetReconnectAttempts: () =>
      set(state => ({
        realtime: { ...state.realtime, reconnectAttempts: 0 },
      })),

    // Navigation Actions
    setCurrentRoute: (currentRoute: string) =>
      set(state => ({
        navigation: {
          previousRoutes: [
            ...state.navigation.previousRoutes,
            state.navigation.currentRoute,
          ].slice(-5),
          currentRoute,
        },
      })),

    // Utility Actions
    setOfflineMode: (offline: boolean) =>
      set(state => ({
        realtime: {
          ...state.realtime,
          isConnected: !offline,
          connectionStatus: offline
            ? 'disconnected'
            : state.realtime.connectionStatus,
        },
      })),

    addNotification: (notification: any) => {
      // Implementation for adding notifications
      console.log('Notification received:', notification);
    },

    cleanupOldNotifications: (daysOld: number) => {
      // Implementation for cleaning up old notifications
      console.log('Cleaning up notifications older than', daysOld, 'days');
    },
      }),
      {
        name: 'app-store',
        partialize: createSelectivePersist([
          'auth',
          'user',
          'ui',
          'objectives',
          'quests',
        ]),
        version: 1,
        onRehydrateStorage: () => (state) => {
          console.log('App store hydrated:', !!state);
          // Reset realtime connection status on hydration
          if (state) {
            state.realtime.isConnected = false;
            state.realtime.connectionStatus = 'disconnected';
            state.realtime.pendingRequests = new Map();
          }
        },
      }
    )
  )
);

// Selectors for optimized component subscriptions
export const useAuth = () => useAppStore(state => state.auth);
export const useUser = () => useAppStore(state => state.user);
export const useObjectives = () => useAppStore(state => state.objectives);
export const useQuests = () => useAppStore(state => state.quests);
export const useChat = () => useAppStore(state => state.chat);
export const useUI = () => useAppStore(state => state.ui);
export const useRealtime = () => useAppStore(state => state.realtime);

// Action selectors
export const useAuthActions = () =>
  useAppStore(state => ({
    setAuthenticated: state.setAuthenticated,
    setUser: state.setUser,
    setAuthLoading: state.setAuthLoading,
    setAuthError: state.setAuthError,
    logout: state.logout,
  }));

export const useQuestActions = () =>
  useAppStore(state => ({
    setTodayQuests: state.setTodayQuests,
    updateQuestStatus: state.updateQuestStatus,
    setQuestCompleting: state.setQuestCompleting,
    addQuest: state.addQuest,
  }));

export const useChatActions = () =>
  useAppStore(state => ({
    addChatMessage: state.addChatMessage,
    addOptimisticMessage: state.addOptimisticMessage,
    setChatTyping: state.setChatTyping,
    addPendingPreview: state.addPendingPreview,
    removePendingPreview: state.removePendingPreview,
  }));

export const useUIActions = () =>
  useAppStore(state => ({
    showToast: state.showToast,
    hideToast: state.hideToast,
    showModal: state.showModal,
    hideModal: state.hideModal,
    setActiveTab: state.setActiveTab,
  }));
