import React from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';

const { width, height } = Dimensions.get('window');

interface LoadingScreenProps {
  message?: string;
  isError?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  isError = false,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {!isError && (
          <ActivityIndicator
            size="large"
            color="#6366F1"
            style={styles.spinner}
          />
        )}
        <Text style={[styles.message, isError && styles.errorMessage]}>
          {message}
        </Text>
        {isError && (
          <Text style={styles.errorSubtext}>
            Please check your connection and try again
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  spinner: {
    marginBottom: 24,
  },
  message: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorMessage: {
    color: '#DC2626',
  },
  errorSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});

export default LoadingScreen;
