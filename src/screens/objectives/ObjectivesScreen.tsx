import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const ObjectivesScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Objectives Screen</Text>
      <Text style={styles.subtitle}>This is the Objectives tab</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
});

export default ObjectivesScreen;
