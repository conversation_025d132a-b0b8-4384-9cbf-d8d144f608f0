buildscript {
    ext {
        buildToolsVersion = "36.0.0"
        minSdkVersion = 24
        compileSdkVersion = 36
        targetSdkVersion = 36
        ndkVersion = "29.0.14033849"
        kotlinVersion = "2.1.20"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.google.gms:google-services:4.4.3")
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:-deprecation" << "-Xlint:-unchecked"
        options.deprecation = false
        options.warnings = false
    }
    
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
        kotlinOptions {
            suppressWarnings = true
        }
    }
}
