/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import { NewAppScreen } from '@react-native/new-app-screen';
import { StatusBar, StyleSheet, useColorScheme, View } from 'react-native';
import {
  SafeAreaProvider,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import firebaseService from './src/services/firebase/firebaseService';
import { useEffect } from 'react';
import * as Sentry from '@sentry/react-native';
import appConfig from "./src/config/appConfig.prod";
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AppNavigator from './src/navigation/AppNavigator';

// Enable screens for react-navigation
import { enableScreens } from 'react-native-screens';
enableScreens();

Sentry.init({
  dsn: appConfig.sentryConfig.dsn,

  sendDefaultPii: true,

  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration()],
  enableNative: false
});

function App() {
  const isDarkMode = useColorScheme() === 'dark';
  useEffect(() => {
    const initializeApp = async () => {
      try{
        firebaseService.initialize();
      }
      catch(error){
        Sentry.captureException(new Error('Failed to initialize Firebase' + error));
        console.error('Failed to initialize Firebase:', error);
      }

      try {
        GoogleSignin.configure({
          webClientId: appConfig.googleSignInConfig.webClientId
        });
      }
      catch(error){
        Sentry.captureException(new Error("Failed to initialize google Sign in" + error));
        console.error('Failed to initialize google Sign in:', error);
      }

    }
    initializeApp();
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <AppContent />
    </SafeAreaProvider>
  );
}

function AppContent() {
  const safeAreaInsets = useSafeAreaInsets();

  return (
    <View style={styles.container}>
      <AppNavigator />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default Sentry.wrap(App);
